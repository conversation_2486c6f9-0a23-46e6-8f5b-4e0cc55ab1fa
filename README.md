# Media Convert Service

Microserviço de conversão de mídia com Python, Celery e FFmpeg. Converte vídeos para MPEG-DASH com múltiplas qualidades e gera thumbnails.

## Funcionalidades

- **Conversão MPEG-DASH**: Vídeos para streaming adaptativo
- **Múltiplas Qualidades**: 720p, 480p, 360p, 240p automáticas
- **Thumbnails**: Geração durante conversão
- **Integração S3**: Download e upload AWS S3
- **Workers Celery**: Processamento distribuído com RabbitMQ
- **Banco de Dados**: Rastreamento PostgreSQL
- **Comunicação Microserviços**: Padrão request-response

## Início <PERSON>

```bash
# Configurar ambiente
cp .env.example .env  # Editar com suas credenciais AWS

# Iniciar serviços
make up      # PostgreSQL + RabbitMQ
make worker  # Worker Celery
```

## Arquitetura

- **Python 3.12** + Celery
- **FFmpeg** para processamento
- **RabbitMQ** como message broker
- **PostgreSQL** para persistência
- **AWS S3** para armazenamento
- **Docker** para containerização

## Uso

### Comunicação entre Microserviços

```python
from app.models.messages import ConversionRequest

# Criar requisição
request = ConversionRequest.create(
    input_s3_path="s3://bucket/video.mp4",
    output_s3_prefix="s3://bucket/dash/video/",
    reply_to_queue="your-service.responses"
)

# Enviar task
celery_app.send_task(
    "app.tasks.microservice_conversion.process_conversion",
    args=[
        request.conversion_id,
        request.input_s3_path,
        request.output_s3_prefix,
        request.reply_to_queue
    ],
    queue="media-convert.requests"
)
```

## Configuração

Variáveis de ambiente no `.env`:
```bash
# AWS
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/media_convert_dev_db

# RabbitMQ (padrões do docker-compose)
RABBITMQ_HOST=localhost
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin
```

## Estrutura de Saída

```
s3://bucket/output-prefix/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```

## Desenvolvimento

### Comandos Docker
```bash
make up            # Iniciar serviços
make worker        # Iniciar worker
make logs          # Ver logs do worker
make down          # Parar serviços
make build         # Build da imagem
make clean         # Reset completo
```

### Comandos de Desenvolvimento
```bash
make test          # Executar testes
make test-cov      # Testes com cobertura
make lint          # Verificar código
make format        # Formatar código
```

### Dependências
```bash
# Instalar dependências
poetry install

# Adicionar nova dependência
poetry add <package>

# Dependências de desenvolvimento
poetry add --group dev <package>
```

## Testes

### Executar Testes
```bash
make test          # Testes básicos
make test-cov      # Com cobertura
make test-watch    # Modo watch
```

### Teste de Integração
```bash
make send-task     # Enviar task de teste
make logs          # Monitorar processamento
```

## Monitoramento

### Status das Conversões
```sql
SELECT id, status, processing_duration_seconds, error_message, created_at
FROM media_conversions
ORDER BY created_at DESC;
```

### Logs
```bash
make logs          # Logs do worker
```

## Qualidade de Código

### SonarQube
```bash
make sonar-reports # Gerar relatórios
make sonar         # Análise (requer SONAR_TOKEN)
```

### Relatórios Gerados
- `.reports/coverage.xml` - Cobertura
- `.reports/test-results.xml` - Resultados dos testes
- `.reports/ruff-report.json` - Qualidade do código
- `.reports/htmlcov/` - Relatório HTML

## Deploy em Produção

### AWS EKS
1. Build da imagem de produção
2. Configurar credenciais AWS e RDS PostgreSQL
3. Setup do cluster RabbitMQ
4. Deploy dos worker pods com limites de recursos
5. Configurar auto-scaling baseado no tamanho da fila

### Estrutura de Branches
- **`main`** → Produção (AWS EKS)
- **`stage`** → Staging (AWS EKS)
- **`feature/*`** → Desenvolvimento
- **`bugfix/*`** → Correções
- **`hotfix/*`** → Correções emergenciais