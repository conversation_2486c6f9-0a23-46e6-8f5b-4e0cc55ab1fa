# Media Convert Service

Media conversion microservice with Python, Celery and FFmpeg. Converts videos to MPEG-DASH with multiple qualities and generates thumbnails.

## Features

- **MPEG-DASH Conversion**: Videos to adaptive streaming
- **Multiple Qualities**: Automatic 720p, 480p, 360p, 240p
- **Thumbnails**: Generated during conversion
- **S3 Integration**: AWS S3 download and upload
- **Celery Workers**: Distributed processing with RabbitMQ
- **Database**: PostgreSQL tracking
- **Microservice Communication**: Request-response pattern

## Quick Start

```bash
# Setup environment
cp .env.example .env  # Edit with your AWS credentials

# Start services
make up      # PostgreSQL + RabbitMQ
make worker  # Celery worker
```

## Architecture

- **Python 3.12** + Celery
- **FFmpeg** for processing
- **RabbitMQ** as message broker
- **PostgreSQL** for persistence
- **AWS S3** for storage
- **Docker** for containerization

## Usage

### Microservice Communication

```python
from app.models.messages import ConversionRequest

# Create request
request = ConversionRequest.create(
    input_s3_path="s3://bucket/video.mp4",
    output_s3_prefix="s3://bucket/dash/video/",
    reply_to_queue="your-service.responses"
)

# Send task
celery_app.send_task(
    "app.tasks.microservice_conversion.process_conversion",
    args=[
        request.conversion_id,
        request.input_s3_path,
        request.output_s3_prefix,
        request.reply_to_queue
    ],
    queue="media-convert.requests"
)
```

## Configuration

Environment variables in `.env`:
```bash
# AWS
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/media_convert_dev_db

# RabbitMQ (docker-compose defaults)
RABBITMQ_HOST=localhost
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin
```

## Output Structure

```
s3://bucket/output-prefix/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```

## Development

### Docker Commands
```bash
make up            # Start services
make worker        # Start worker
make logs          # View worker logs
make down          # Stop services
make build         # Build image
make clean         # Complete reset
```

### Development Commands
```bash
make test          # Run tests
make test-cov      # Tests with coverage
make lint          # Check code
make format        # Format code
```

### Dependencies
```bash
# Install dependencies
poetry install

# Add new dependency
poetry add <package>

# Development dependencies
poetry add --group dev <package>
```

## Testing

### Run Tests
```bash
make test          # Basic tests
make test-cov      # With coverage
make test-watch    # Watch mode
```

### Integration Testing
```bash
make send-task     # Send test task
make logs          # Monitor processing
```

## Monitoring

### Conversion Status
```sql
SELECT id, status, processing_duration_seconds, error_message, created_at
FROM media_conversions
ORDER BY created_at DESC;
```

### Logs
```bash
make logs          # Worker logs
```

## Code Quality

### SonarQube
```bash
make sonar-reports # Generate reports
make sonar         # Analysis (requires SONAR_TOKEN)
```

### Generated Reports
- `.reports/coverage.xml` - Coverage
- `.reports/test-results.xml` - Test results
- `.reports/ruff-report.json` - Code quality
- `.reports/htmlcov/` - HTML report

## Production Deployment

### AWS EKS
1. Build production image
2. Configure AWS credentials and RDS PostgreSQL
3. Setup RabbitMQ cluster
4. Deploy worker pods with resource limits
5. Configure auto-scaling based on queue length

### Branch Structure
- **`main`** → Production (AWS EKS)
- **`stage`** → Staging (AWS EKS)
- **`feature/*`** → Development
- **`bugfix/*`** → Bug fixes
- **`hotfix/*`** → Emergency fixes