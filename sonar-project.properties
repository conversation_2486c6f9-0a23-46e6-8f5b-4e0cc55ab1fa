# SonarQube Configuration for Media Convert Microservice
sonar.projectKey=media-convert-microservice
sonar.projectName=Media Convert Microservice
sonar.projectVersion=1.0.0

# Source code and tests
sonar.sources=app
sonar.tests=tests
sonar.sourceEncoding=UTF-8
sonar.python.version=3.12

# Language-specific settings
sonar.language=py

# Coverage and test reports (Community Edition compatible)
sonar.python.coverage.reportPaths=.reports/coverage.xml

# External linter reports (Ruff integration)
sonar.python.ruff.reportPaths=.reports/ruff-report.json

# Analysis exclusions
sonar.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__pycache__/**,**/temp/**,**/logs/**,**/.venv/**,**/.pytest_cache/**,**/.mypy_cache/**,**/.ruff_cache/**

# Coverage exclusions
sonar.coverage.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__init__.py,**/conftest.py,**/test_*.py

# Duplicate code detection exclusions
sonar.cpd.exclusions=**/migrations/**,tests/**,**/__init__.py

# Quality gate settings (Community Edition)
sonar.qualitygate.wait=true

# Performance settings
sonar.verbose=false
sonar.log.level=INFO
