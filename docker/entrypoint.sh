#!/bin/bash

# Media Convert Service - Simple Docker Entrypoint
# Starts Celery worker directly with configuration from environment variables

set -e

# Environment variables with defaults
CELERY_APP=${CELERY_APP:-"app.celery_app"}
CELERY_LOGLEVEL=${CELERY_LOGLEVEL:-"info"}
CELERY_QUEUES=${CELERY_QUEUES:-"media-convert.requests"}
CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
CELERY_MAX_TASKS_PER_CHILD=${CELERY_MAX_TASKS_PER_CHILD:-1000}

echo "Starting Media Convert Celery Worker"
echo "Configuration: App=${CELERY_APP}, Queues=${CELERY_QUEUES}, Concurrency=${CELERY_CONCURRENCY}"

# Create directories and set permissions
mkdir -p /var/log/celery /tmp/media-convert

# Try to set permissions, but don't fail if we can't (Kubernetes handles this)
if [ "$(id -u)" = "0" ]; then
    chown -R celery:celery /var/log/celery /tmp/media-convert 2>/dev/null || echo "Warning: Could not change ownership (Kubernetes will handle permissions)"
else
    echo "Running as non-root user, skipping chown (Kubernetes handles permissions)"
fi

# Run database migrations
echo "Running database migrations..."
chmod +x /app/scripts/run-migrations.sh

# Debug: Check current Python environment
echo "Current Python path: $(which python)"
echo "Checking SQLAlchemy installation..."
python -c "import sqlalchemy; print('SQLAlchemy version:', sqlalchemy.__version__)" || echo "SQLAlchemy not found"

/app/scripts/run-migrations.sh

# Start Celery worker
# If running as root, use gosu to switch to celery user
# If already running as celery user (Kubernetes), run directly
if [ "$(id -u)" = "0" ]; then
    echo "Running as root, switching to celery user with gosu"
    exec gosu celery celery -A ${CELERY_APP} worker \
        --loglevel=${CELERY_LOGLEVEL} \
        --queues=${CELERY_QUEUES} \
        --concurrency=${CELERY_CONCURRENCY} \
        --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD}
else
    echo "Running as user $(whoami), starting Celery directly"
    exec celery -A ${CELERY_APP} worker \
        --loglevel=${CELERY_LOGLEVEL} \
        --queues=${CELERY_QUEUES} \
        --concurrency=${CELERY_CONCURRENCY} \
        --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD}
fi