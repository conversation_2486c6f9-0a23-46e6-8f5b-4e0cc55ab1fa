from celery.apps.worker import Worker
from loguru import logger


def load_banner_from_file(filepath: str) -> str:
    try:
        with open(filepath, encoding="utf-8") as f:
            return f.read()
    except (FileNotFoundError, PermissionError, OSError) as e:
        logger.warning(f"No banner found. {e}")
        return "Media-Convert Service"


def custom_banner():
    ascii_art = load_banner_from_file("app/utils/banner.txt")
    logger.info(ascii_art)


_original_on_start = Worker.on_start


def _patched_on_start(self, *args, **kwargs):  # type: ignore[override]
    custom_banner()
    return _original_on_start(self, *args, **kwargs)


Worker.on_start = _patched_on_start  # type: ignore[assignment]
