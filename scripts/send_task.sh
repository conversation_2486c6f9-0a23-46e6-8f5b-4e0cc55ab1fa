#!/bin/bash

# Carrega variáveis de ambiente
set -a
source .env 2>/dev/null || echo "Warning: .env file not found"
set +a

# Gera um ID único para o teste
TEST_ID=$(uuidgen 2>/dev/null || python3 -c "import uuid; print(uuid.uuid4())")

echo "Enviando task de teste com ID: $TEST_ID"

poetry run python -c "
import uuid
from examples.send_conversion_task import send

# Usar dados de exemplo para teste
result = send(
    conversion_id='$TEST_ID',
    input_s3_path='s3://your-bucket/test-video.mp4',
    output_s3_prefix='s3://your-bucket/output/test-conversion',
    reply_to_queue='media-convert.responses',
    media_type='video',
    output_format='dash'
)

print(f'Task enviada com sucesso!')
print(f'Conversion ID: $TEST_ID')
print(f'Celery Task ID: {result.id}')
print(f'Monitore com: make logs')
"