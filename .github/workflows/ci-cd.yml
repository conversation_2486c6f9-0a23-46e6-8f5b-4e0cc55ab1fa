
name: CI/CD Pipeline

on:
  pull_request:
    branches:
      - develop
    types: [opened, synchronize, reopened]
  push:
    branches:
      - main
      - develop
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.12'
  POETRY_VERSION: '2.1.3'
  AWS_REGION: 'us-east-1'
  ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}

jobs:
  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Poetry dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry
            ~/.cache/pip
            .venv
          key: ${{ runner.os }}-poetry-${{ hashFiles('poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-

      - name: Install Poetry
        run: pip install poetry==${{ env.POETRY_VERSION }}

      - name: Install dependencies
        run: poetry install --no-root

      - name: Run Ruff linting
        run: make lint

      - name: Run Ruff formatting check
        run: make format-check

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Poetry dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry
            ~/.cache/pip
            .venv
          key: ${{ runner.os }}-poetry-${{ hashFiles('poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-

      - name: Install Poetry
        run: pip install poetry==${{ env.POETRY_VERSION }}

      - name: Install dependencies
        run: poetry install --no-root

      - name: Run Tests with Coverage
        run: make sonar-reports

      - name: Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-reports
          path: .reports/
          include-hidden-files: true
          retention-days: 5

  sonar:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download test reports
        uses: actions/download-artifact@v4
        with:
          name: test-reports
          path: .reports/

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: SonarQube Quality Gate Check
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  deploy:
    name: Deploy to EKS
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    needs: [lint, test, sonar]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install AWS CLI (for act)
        if: ${{ env.ACT }}
        run: |
          sudo apt-get update && \
          sudo apt-get install -y unzip curl && \
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
          unzip awscliv2.zip && \
          sudo ./aws/install

      - name: Install kubectl (for act)
        if: ${{ env.ACT }}
        run: |
          KUBECTL_VERSION=v1.30.0
          curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl"
          chmod +x kubectl
          sudo mv kubectl /usr/local/bin/
          kubectl version --client
      - name: Configure kubeconfig
        run: aws eks update-kubeconfig --region us-east-1 --name keeps-eks-cluster

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }}             | docker login --username AWS --password-stdin ${{ env.ECR_REPOSITORY }}

      - name: Build and Push Docker Image
        run: |
          if [[ "${GITHUB_REF}" == "refs/heads/main" ]]; then
              IMAGE_TAG="production"
          else
              IMAGE_TAG="stage"
          fi

          docker build -t ${{ env.ECR_REPOSITORY }}:$IMAGE_TAG -f docker/Dockerfile .
          docker push ${{ env.ECR_REPOSITORY }}:$IMAGE_TAG

      - name: Deploy to EKS
        run: |
          if [[ "${GITHUB_REF}" == "refs/heads/main" ]]; then
              NAMESPACE="production"
          else
              NAMESPACE="stage"
          fi
          kubectl rollout restart deployment/media-convert -n $NAMESPACE
          kubectl rollout status deployment/media-convert -n $NAMESPACE
