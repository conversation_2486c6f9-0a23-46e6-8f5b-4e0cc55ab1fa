# Media Convert Service - Makefile

PYTHON_VERSION := 3.12.7
POETRY := poetry

.PHONY: help env reset-env reinstall-python up worker down logs build install test test-cov test-watch lint format clean sonar sonar-reports

# Default target
help:
	@echo "Media Convert Service Commands:"
	@echo "  make up         - Start services (PostgreSQL, RabbitMQ)"
	@echo "  make worker     - Start Celery worker"
	@echo "  make down       - Stop services"
	@echo "  make logs       - View worker logs"
	@echo "  make build      - Build worker image"
	@echo ""
	@echo "Development Commands:"
	@echo "  make install    - Install dependencies with Poetry"
	@echo "  make test       - Run all tests"
	@echo "  make test-cov   - Run tests with coverage"
	@echo "  make test-watch - Run tests in watch mode (fast, no coverage)"
	@echo "  make lint       - Run linting"
	@echo "  make format     - Format code"
	@echo "  make clean      - Remove containers, caches and reports"
	@echo ""
	@echo "SonarQube Commands:"
	@echo "  make sonar-reports - Generate reports for SonarQube"
	@echo "  make sonar         - Run SonarQube analysis"


# Poetry environment management
reinstall-python:
	@echo "Reinstalling Python $(PYTHON_VERSION) via pyenv..."
	@pyenv uninstall -f $(PYTHON_VERSION) || true
	@pyenv install $(PYTHON_VERSION)

env:
	@echo ">> Creating new Poetry environment with Python $(PYTHON_VERSION)..."
	@$(POETRY) env remove --all || true
	@$(POETRY) env use python$(pyenv which PYTHON_VERSION)
	@$(POETRY) install
	@echo ">> Environment ready!"

# Services
up:
	@mkdir -p logs temp
	@docker-compose up -d postgres rabbitmq

worker:
	@docker-compose --profile worker up -d media-convert-worker

down:
	@docker-compose --profile worker down -v

logs:
	@docker-compose logs -f media-convert-worker

build:
	@docker-compose build media-convert-worker

# Development
install:
	@echo "Installing dependencies with Poetry..."
	@$(POETRY) install
	@echo "Dependencies installed successfully!"

test:
	@echo "Running all tests..."
	@$(POETRY) run pytest -v --tb=short
	@echo "Tests completed!"

test-cov:
	@echo "Running tests with coverage..."
	@mkdir -p .reports/htmlcov
	@$(POETRY) run pytest -v --cov=app --cov-report=term-missing \
		--cov-report=html:.reports/htmlcov \
		--cov-report=xml:.reports/coverage.xml
	@echo "Coverage report generated in .reports/"

test-watch:
	@echo "Running tests in watch mode (Ctrl+C to stop)..."
	@$(POETRY) run ptw -- -q

lint:
	@echo "Running linting..."
	@$(POETRY) run ruff check app/
	@echo "Linting completed!"

lint-fix:
	@echo "Running linting..."
	@$(POETRY) run ruff check app/ --fix

type-check:
	@echo "Running type checking with mypy..."
	@poetry run mypy app/ --ignore-missing-imports --strict
	@echo "Type checking completed!"

format:
	@echo "Formatting code..."
	@$(POETRY) run ruff format app/ tests/
	@echo "Code formatted!"

format-check:
	@echo "Formatting code..."
	@$(POETRY) run ruff format --check app/ tests/
	@echo "Code formatted!"

# SonarQube
sonar:
	docker run \
		--rm \
		-e SONAR_HOST_URL="https://sonar.keepsdev.com" \
		-e SONAR_SCANNER_OPTS="-Dsonar.projectKey=media-convert-microservice" \
		-e SONAR_TOKEN="${SONAR_TOKEN}" \
		-v "${PWD}/:/usr/src" \
		sonarsource/sonar-scanner-cli

sonar-reports:
	@echo "Generating reports for SonarQube..."
	@mkdir -p .reports
	@$(POETRY) run pytest -v --cov=app \
		--cov-report=xml:.reports/coverage.xml \
		--cov-report=term-missing \
		--junitxml=.reports/test-results.xml \
		--cov-report=html:.reports/htmlcov
	@$(POETRY) run ruff check app/ --output-format=json > .reports/ruff-report.json || true
	@sed -i 's|<source>.*</source>|<source>app</source>|' .reports/coverage.xml
	@jq 'map(.filename |= sub("^.*/media-convert/"; ""))' .reports/ruff-report.json > .reports/ruff-fixed.json
	@mv .reports/ruff-fixed.json .reports/ruff-report.json
	@echo "$(cat .reports/coverage.xml)"
	@echo "Reports generated successfully in .reports/ directory."

# Cleanup
clean:
	@echo "Complete project cleanup..."
	@docker-compose --profile worker down -v || true
	@find $(CURDIR) -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@rm -rf $(CURDIR)/.pytest_cache $(CURDIR)/.coverage $(CURDIR)/htmlcov || true
	@rm -rf $(CURDIR)/.mypy_cache $(CURDIR)/.ruff_cache $(CURDIR)/logs $(CURDIR)/temp $(CURDIR)/.reports || true
	@$(POETRY) cache clear pypi --all || true
	@rm -rf $(CURDIR)/.venv || true
	@echo "Project completely cleaned!"
