import os
import sys
import uuid

from celery import Celery

# Add app to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.config import RabbitMQConfig
from app.database.models import MediaType, OutputFormat


# Para execução local, usar localhost em vez do nome do container
class LocalRabbitMQConfig(RabbitMQConfig):
    HOST = "localhost"

broker_url = LocalRabbitMQConfig.get_broker_url()

# Celery Initilize
celery = Celery(broker=broker_url)

def send(
    conversion_id: str,
    input_s3_path: str,
    output_s3_prefix: str,
    reply_to_queue: str,
    media_type: str, #MediaType.VIDEO,
    output_format: str, #OutputFormat.DASH
):
    """
    Envia a task Celery com os dados fornecidos.

    Args:
        conversion_id: Unique conversion identifier
        input_s3_path: S3 path to input media file
        output_s3_prefix: S3 prefix for output files
        reply_to_queue: Queue for response messages
        media_type: Type of media (video, image, audio)
        output_format: Target output format (dash, webp, etc.)
    """
    payload = {
        "conversion_id": conversion_id,
        "input_s3_path": input_s3_path,
        "output_s3_prefix": output_s3_prefix,
        "reply_to_queue": reply_to_queue,
        "media_type": media_type,
        "output_format": output_format
    }

    result = celery.send_task(
        "app.tasks.microservice_conversion.handle_conversion_request",
        args=[payload],
        queue="media-convert.requests"
    )

    print(f"Task sent successfully! Conversion ID: {conversion_id}")
    return result
